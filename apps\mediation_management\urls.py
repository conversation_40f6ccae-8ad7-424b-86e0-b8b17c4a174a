#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/17
@File_Desc: 调解管理URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views.mediation_plan_view_set import MediationPlanViewSet
from .views.mediation_case_view_set import MediationCaseViewSet
from .views.mediation_case_views import (
    MediationCaseByDebtorView,
    MediationContentView,
    MediationPlanConfigView,
    MediationCaseListAPIView,
    MediationAgreementPDFView
)

# 创建主路由器并注册视图集
router = DefaultRouter()
router.register(r'mediation_plan', MediationPlanViewSet, basename='mediation_plan')
router.register(r'mediation_case', MediationCaseViewSet, basename='mediation_case')

# 创建调解案件子路由配置
mediation_case_urlpatterns = [
    # 调解案件按债务人查询接口 - 根据债务人姓名和身份证号统计调解案件数量
    path('wechat/by_debtor/', MediationCaseByDebtorView.as_view(), name='mediation_case_wechat_by_debtor'),
    # 调解信息内容获取接口 - 根据调解案件ID获取处理后的调解配置信息
    path('<int:mediation_case_id>/content/', MediationContentView.as_view(), name='mediation_case_content'),
    # 调解方案配置获取接口 - 根据调解案件ID获取相关调解方案的配置信息
    path('<int:mediation_case_id>/plan_config/', MediationPlanConfigView.as_view(), name='mediation_case_plan_config'),
    # 微信小程序调解案件列表查询接口 - 根据已认证用户查询相关调解案件列表
    path('wechat/list/', MediationCaseListAPIView.as_view(), name='mediation_case_wechat_list'),
    # 调解协议PDF生成和下载接口 - 根据调解案件ID和案件号生成PDF协议文件
    path('wechat/<int:case_id>/agreement_pdf/', MediationAgreementPDFView.as_view(), name='mediation_case_wechat_agreement_pdf'),
    # 微信小程序调解案件状态确认接口 - 将案件状态从"待确认"更新为"进行中"
    path('wechat/<int:pk>/confirm_status/', MediationCaseViewSet.as_view({'put': 'confirm_status'}), name='mediation_case_wechat_confirm_status'),
    # 微信小程序调解方案更新接口 - 更新调解案件关联的调解方案信息
    path('wechat/<int:pk>/update_mediation_plan/', MediationCaseViewSet.as_view({'put': 'update_mediation_plan'}), name='mediation_case_wechat_update_plan'),
    # 微信小程序电子签名更新接口 - 更新调解案件的电子签名文件
    path('wechat/<int:pk>/update_electronic_signature/', MediationCaseViewSet.as_view({'put': 'update_electronic_signature'}), name='mediation_case_wechat_update_signature'),
]

# URL配置
urlpatterns = [
    path('', include(router.urls)),
    # 调解案件相关子路由 - 嵌套在 /mediation_management/mediation_case/ 路径下
    path('mediation_case/', include(mediation_case_urlpatterns)),
]

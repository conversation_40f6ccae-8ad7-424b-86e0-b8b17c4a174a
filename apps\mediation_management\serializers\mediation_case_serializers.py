#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_serializers.py
<AUTHOR> JT_DA
@Date     : 2025/07/17
@File_Desc: 调解案件序列化器
"""

from rest_framework import serializers
from django.db import transaction
from django.utils import timezone

from apps.mediation_management.models import MediationCase, MediationPlan, MediationCaseFile
from apps.counterparty.models import DebtorBasicInfo, CreditorBasicInfo
from apps.data_governance.models import AssetPackageManagement
from apps.user.models import SystemUser


def validate_attachment_file(file_data):
    """公共附件文件验证方法 - 验证附件文件格式和大小"""
    # 检查文件大小（限制为10MB）
    max_size = 10 * 1024 * 1024  # 10MB
    if file_data.size > max_size:
        raise serializers.ValidationError("附件文件大小不能超过10MB")

    # 检查文件扩展名
    allowed_extensions = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".jpg", ".jpeg", ".png", ".gif"]
    file_name = file_data.name.lower()
    if not any(file_name.endswith(ext) for ext in allowed_extensions):
        raise serializers.ValidationError(
            "附件文件格式不支持，仅支持：pdf、doc、docx、xls、xlsx、txt、jpg、jpeg、png、gif"
        )

    return file_data


class MediationCaseFileSerializer(serializers.ModelSerializer):
    """调解案件附件序列化器 - 用于处理附件文件，包含安全下载链接"""

    # 安全下载链接 - 使用UUID标识符生成的安全下载URL
    secure_download_url = serializers.SerializerMethodField(
        help_text="安全下载链接，使用UUID标识符防止路径暴露"
    )

    class Meta:
        model = MediationCaseFile
        fields = ["id", "file_name", "secure_token", "secure_download_url"]

    def get_secure_download_url(self, obj):
        """生成安全的文件下载链接"""
        from utils.file_security_helper import FileSecurityHelper
        return FileSecurityHelper.generate_secure_download_url(obj)


class MediationCaseListSerializer(serializers.ModelSerializer):
    """调解案件列表序列化器 - 用于列表展示"""

    # 关联字段的中文显示
    debtor_name = serializers.CharField(source="debtor.debtor_name", read_only=True)
    creditor_name = serializers.CharField(source="creditor.creditor_name", read_only=True)
    mediator_name = serializers.CharField(source="mediator.username", read_only=True)
    mediation_plan_name = serializers.CharField(source="mediation_plan.plan_name", read_only=True)
    asset_package_name = serializers.CharField(source="asset_package.package_name", read_only=True)

    # 状态字段中文显示
    case_status_cn = serializers.CharField(source="get_case_status_display", read_only=True)
    notarization_status_cn = serializers.CharField(source="get_notarization_status_display", read_only=True)

    # 时间字段格式化显示
    initiate_date = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    close_date = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    signature_date = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    # 文件字段显示
    electronic_signature_name = serializers.SerializerMethodField()

    # 调解配置信息 - 通过关联的资产包获取
    mediation_config = serializers.SerializerMethodField(help_text="调解信息配置，从关联的资产包获取")

    # 案件附件信息
    attachments = MediationCaseFileSerializer(many=True, read_only=True)
    attachments_count = serializers.SerializerMethodField()
    file_cn = serializers.SerializerMethodField(read_only=True, help_text="附件（中文描述）")

    # 映射字段名称列表 - 从关联的资产包获取字段映射配置
    mapped_field_names = serializers.SerializerMethodField()

    class Meta:
        model = MediationCase
        fields = [
            "id",
            "case_number",
            "case_status",
            "case_status_cn",
            "debtor",
            "debtor_name",
            "creditor",
            "creditor_name",
            "mediator",
            "mediator_name",
            "mediation_config",
            "confirmed_mediation_config",
            "confirmed_plan_config",
            "mediation_plan",
            "mediation_plan_name",
            "asset_package",
            "asset_package_name",
            "asset_package_row_number",
            "initiate_date",
            "close_date",
            "signature_date",
            "electronic_signature",
            "electronic_signature_name",
            "notarization_status",
            "notarization_status_cn",
            "attachments",
            "attachments_count",
            "file_cn",
            "mapped_field_names",
        ]

    def get_electronic_signature_name(self, obj):
        """获取电子签名文件名"""
        if not obj.electronic_signature:
            return None
        return obj.electronic_signature.name.split("/")[-1] if obj.electronic_signature.name else None

    def get_attachments_count(self, obj):
        """获取案件附件数量"""
        return obj.attachments.count()

    def get_file_cn(self, obj):
        # Serialize the related ProjectJobFile instances
        serializer = MediationCaseFileSerializer(obj.attachments.all(), many=True)
        serialized_data = serializer.data

        # Check if there are any files attached
        if serialized_data:
            # If there's only one file, return its name directly
            if len(serialized_data) == 1:
                return serialized_data[0]["file_name"]
            else:
                # If there are multiple files, concatenate their names with their index
                return "\n".join([f"{idx + 1}、{item['file_name']}" for idx, item in enumerate(serialized_data)])
        else:
            return ""

    def get_mediation_config(self, obj):
        """获取调解信息配置 - 从关联的资产包获取"""
        if obj.asset_package and obj.asset_package.mediation_config:
            return obj.asset_package.mediation_config
        return None

    def get_mapped_field_names(self, obj):
        """
        获取映射字段名称列表

        获取逻辑：
        - 直接通过 asset_package 字段获取资产包对象
        - 如果 asset_package 为 null，则返回空列表

        Returns:
            list: 映射字段名称列表，只包含有 mapped_field_config 配置的字段名称
        """
        # 获取资产包对象
        if not obj.asset_package:
            return []

        # 获取资产包的字段映射关系
        mappings = obj.asset_package.field_mappings.all()

        field_names = []
        for mapping in mappings:
            # 只提取有映射配置的字段名称
            if mapping.mapped_field_config:
                field_names.append(mapping.mapped_field_config.field_name)

        return field_names


# 注意：MediationCaseCreateSerializer 已删除
# 调解案件现在通过资产包更新自动创建，不再支持直接创建


class MediationCaseUpdateSerializer(serializers.ModelSerializer):
    """调解案件基础更新序列化器 - 用于基础信息更新

    注意：mediation_config 字段已移除，调解配置信息现在通过关联的资产包获取
    """

    # 支持多文件上传 - 接收文件列表用于创建附件
    file = serializers.ListField(
        child=serializers.FileField(), required=False, write_only=True, help_text="上传的附件文件列表，支持多文件上传"
    )

    # 支持附件ID列表 - 用于保留现有附件
    file_id = serializers.ListField(
        child=serializers.IntegerField(), required=False, write_only=True, help_text="需要保留的附件ID列表"
    )

    class Meta:
        model = MediationCase
        fields = [
            "debtor",
            "file",
            "file_id",
        ]

    # 注意：validate_mediation_config 方法已移除，因为 mediation_config 字段已删除

    def validate_debtor(self, value):
        """验证债务人是否存在"""
        if value and not DebtorBasicInfo.objects.filter(id=value.id).exists():
            raise serializers.ValidationError("指定的债务人不存在")
        return value

    def validate_creditor(self, value):
        """验证债权人是否存在"""
        if value and not CreditorBasicInfo.objects.filter(id=value.id).exists():
            raise serializers.ValidationError("指定的债权人不存在")
        return value

    def validate_asset_package(self, value):
        """验证资产包是否存在"""
        if value and not AssetPackageManagement.objects.filter(id=value.id).exists():
            raise serializers.ValidationError("指定的资产包不存在")
        return value

    def validate_file_id(self, value):
        """验证附件ID列表的有效性"""
        if value:
            # 检查所有附件ID是否存在
            existing_ids = set(MediationCaseFile.objects.filter(id__in=value).values_list("id", flat=True))
            invalid_ids = set(value) - existing_ids
            if invalid_ids:
                raise serializers.ValidationError(f"以下附件ID不存在：{list(invalid_ids)}")
        return value

    def update(self, instance, validated_data):
        """更新调解案件基础信息并处理附件文件"""
        # 提取文件相关数据
        file_ids = validated_data.pop("file_id", [])
        files_data = validated_data.pop("file", [])

        with transaction.atomic():
            try:
                # 更新基础字段
                for attr, value in validated_data.items():
                    setattr(instance, attr, value)
                instance.save()

                # 处理附件关联关系
                if file_ids is not None:  # 如果提供了file_id列表，则重置附件关联
                    # 获取有效的附件对象
                    existing_attachments = MediationCaseFile.objects.filter(id__in=file_ids)
                    instance.attachments.set(existing_attachments)

                # 处理新上传的附件文件
                if files_data:
                    new_attachment_files = []
                    for file_data in files_data:
                        # 验证文件格式和大小
                        validate_attachment_file(file_data)

                        # 创建附件记录
                        attachment = MediationCaseFile.objects.create(file=file_data, file_name=file_data.name)
                        new_attachment_files.append(attachment)

                    # 追加新的附件到现有关联关系中
                    instance.attachments.add(*new_attachment_files)

                return instance

            except Exception as e:
                # 记录错误并重新抛出
                raise serializers.ValidationError(f"更新调解案件时发生错误：{str(e)}")


class MediationCaseMediatorUpdateSerializer(serializers.ModelSerializer):
    """调解案件调解员更新序列化器 - 仅处理mediator字段"""

    class Meta:
        model = MediationCase
        fields = ["mediator"]

    def validate_mediator(self, value):
        """验证调解员是否存在且有效"""
        if value and not SystemUser.objects.filter(id=value.id, is_active=True).exists():
            raise serializers.ValidationError("指定的调解员不存在或已禁用")
        return value

    def update(self, instance, validated_data):
        """更新调解员信息"""
        with transaction.atomic():
            instance.mediator = validated_data.get("mediator")
            instance.save()
            return instance


class MediationCasePlanUpdateSerializer(serializers.ModelSerializer):
    """调解案件调解方案更新序列化器 - 仅处理mediation_plan字段"""

    class Meta:
        model = MediationCase
        fields = ["mediation_plan"]

    def validate_mediation_plan(self, value):
        """验证调解方案是否存在且已审批通过"""
        if value:
            if not MediationPlan.objects.filter(id=value.id).exists():
                raise serializers.ValidationError("指定的调解方案不存在")
            if value.approval_status != "approved":
                raise serializers.ValidationError("只能选择已审批通过的调解方案")
        return value

    def validate(self, data):
        """全局验证 - 检查是否已关联调解方案"""
        mediation_plan = data.get("mediation_plan")

        # 如果当前案件已经关联了调解方案，且新的调解方案不为空，则不允许更新
        if self.instance.mediation_plan and mediation_plan:
            raise serializers.ValidationError("调解案件已关联调解方案，不支持更新。如需更改，请先取消当前方案关联。")

        return data

    def update(self, instance, validated_data):
        """更新调解方案信息，并在确认方案时存储配置快照"""
        with transaction.atomic():
            mediation_plan = validated_data.get("mediation_plan")

            # 更新调解方案关联
            instance.mediation_plan = mediation_plan

            # 如果用户确认了调解方案（不为空），则存储配置快照
            if mediation_plan:
                # 获取并存储资产包的调解配置信息
                if instance.asset_package and instance.asset_package.mediation_config:
                    instance.confirmed_mediation_config = instance.asset_package.mediation_config

                # 获取并存储调解方案的配置信息
                if mediation_plan.plan_config:
                    instance.confirmed_plan_config = mediation_plan.plan_config
            else:
                # 如果取消方案关联，清空确认的配置信息
                instance.confirmed_mediation_config = None
                instance.confirmed_plan_config = None

            instance.save()
            return instance


class MediationCaseSignatureUpdateSerializer(serializers.ModelSerializer):
    """调解案件电子签名更新序列化器 - 仅处理electronic_signature字段"""

    class Meta:
        model = MediationCase
        fields = ["electronic_signature"]

    def validate_electronic_signature(self, value):
        """验证电子签名文件格式"""
        if value:
            # 检查文件扩展名
            allowed_extensions = [".jpg", ".jpeg", ".png", ".gif"]
            file_name = value.name.lower()
            if not any(file_name.endswith(ext) for ext in allowed_extensions):
                raise serializers.ValidationError("电子签名文件只支持图片格式（jpg、jpeg、png、gif）")

            # 检查文件大小（限制为5MB）
            if value.size > 5 * 1024 * 1024:
                raise serializers.ValidationError("电子签名文件大小不能超过5MB")

        return value

    def update(self, instance, validated_data):
        """更新电子签名信息"""
        with transaction.atomic():
            # 更新电子签名文件
            instance.electronic_signature = validated_data.get("electronic_signature")

            # 当电子签名更新时，自动记录签署日期并将案件状态调整为"已完成"
            if instance.electronic_signature:
                instance.signature_date = timezone.now()  # 记录当前日期时间作为签署日期
                instance.case_status = "completed"  # 将案件状态调整为"已完成"

            instance.save()
            return instance


class BatchUpdateStatusSerializer(serializers.Serializer):
    """批量更新调解案件状态序列化器 - 用于批量将案件状态从"待发起"更新为"已发起" """

    # 案件ID列表 - 需要更新状态的案件ID列表
    case_ids = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        min_length=1,
        required=True,
        help_text="需要更新状态的案件ID列表，支持任意数量的批量操作，超过100个案件时将自动使用异步处理"
    )

    def validate_case_ids(self, value):
        """验证案件ID列表的有效性和状态条件"""
        if not value:
            raise serializers.ValidationError("案件ID列表不能为空")

        # 去重处理
        unique_case_ids = list(set(value))
        if len(unique_case_ids) != len(value):
            raise serializers.ValidationError("案件ID列表中存在重复项")

        # 批量查询案件是否存在
        existing_cases = MediationCase.objects.filter(id__in=unique_case_ids)
        existing_case_ids = set(existing_cases.values_list('id', flat=True))

        # 检查不存在的案件ID
        missing_case_ids = set(unique_case_ids) - existing_case_ids
        if missing_case_ids:
            raise serializers.ValidationError(f"以下案件ID不存在：{sorted(list(missing_case_ids))}")

        # 检查案件状态是否为"待发起"
        invalid_status_cases = existing_cases.exclude(case_status='draft')
        if invalid_status_cases.exists():
            invalid_cases_info = [
                f"案件{case.case_number}(ID:{case.id})当前状态为{case.get_case_status_display()}"
                for case in invalid_status_cases
            ]
            raise serializers.ValidationError(
                f"以下案件状态不是'待发起'，无法更新：{'; '.join(invalid_cases_info)}"
            )

        return unique_case_ids

    def update_cases_status(self):
        """批量更新案件状态为"已发起"并设置发起时间"""
        from django.utils import timezone
        from django.db import transaction

        case_ids = self.validated_data['case_ids']

        with transaction.atomic():
            # 批量更新案件状态和发起时间
            updated_count = MediationCase.objects.filter(
                id__in=case_ids,
                case_status='draft'  # 双重保险，确保只更新待发起状态的案件
            ).update(
                case_status='initiated',
                initiate_date=timezone.now()
            )

            # 获取更新后的案件信息用于返回
            updated_cases = MediationCase.objects.filter(
                id__in=case_ids
            ).values('id', 'case_number', 'case_status', 'initiate_date')

            return {
                'updated_count': updated_count,
                'updated_cases': list(updated_cases)
            }
